{"cells": [{"cell_type": "markdown", "id": "41bf356f-2777-4b3c-83ca-197705a42ac4", "metadata": {}, "source": ["# Sleep Dataset Generator\n", "\n", "By <PERSON>\n", "Released under the MIT License\n", "\n", "This script creates a fictional set of sleep data that can serve as the data source for our baby_sleep_analysis script.\n", "\n", "**Note: this dataset is *NOT* intended to be an accurate representation of typical infant sleep schedules!**\n"]}, {"cell_type": "code", "execution_count": 1, "id": "2ccd8b9b-c7b4-4d9c-9460-4b7dfa7e9680", "metadata": {"execution": {"iopub.execute_input": "2024-05-14T16:16:00.309405Z", "iopub.status.busy": "2024-05-14T16:16:00.308403Z", "iopub.status.idle": "2024-05-14T16:16:00.764993Z", "shell.execute_reply": "2024-05-14T16:16:00.764993Z", "shell.execute_reply.started": "2024-05-14T16:16:00.309405Z"}}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import time"]}, {"cell_type": "markdown", "id": "0a379f36-948b-4de0-b457-5b4ce55fa793", "metadata": {"execution": {"iopub.execute_input": "2024-05-11T11:20:06.732036Z", "iopub.status.busy": "2024-05-11T11:20:06.731051Z", "iopub.status.idle": "2024-05-11T11:20:06.735671Z", "shell.execute_reply": "2024-05-11T11:20:06.735671Z", "shell.execute_reply.started": "2024-05-11T11:20:06.731051Z"}}, "source": ["Setting the date and time of birth: (make sure to express the time in 24-hour format.)"]}, {"cell_type": "code", "execution_count": 2, "id": "a13ab565-fe5f-438c-81c0-4ecf9595c5c7", "metadata": {"execution": {"iopub.execute_input": "2024-05-14T16:16:00.766013Z", "iopub.status.busy": "2024-05-14T16:16:00.766013Z", "iopub.status.idle": "2024-05-14T16:16:00.774287Z", "shell.execute_reply": "2024-05-14T16:16:00.774287Z", "shell.execute_reply.started": "2024-05-14T16:16:00.766013Z"}}, "outputs": [{"data": {"text/plain": ["(Timestamp('2024-01-15 16:31:00'), Timestamp('2024-01-15 17:43:00'), 0)"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["datob = pd.to_datetime('2024-01-15 13:37') # datob = 'date and time of birth'\n", "# Creating our first sleep record, which we can then use to initialize\n", "# our sleep DataFrame:\n", "sleep_start = datob + pd.to_<PERSON><PERSON><PERSON>(174, unit = 'minutes')\n", "sleep_end = sleep_start + pd.to_timed<PERSON>ta(72, unit = 'minutes')\n", "\n", "# Creating a days_old value: (These values will be useful in determining how to \n", "# simulate our sleep start and end times, since the sleep onset and duration\n", "# times will change as the baby gets older):\n", "\n", "days_old = (sleep_start - datob).days\n", "\n", "# Determining when each night should begin and end: (This will be relevant\n", "# to our calculations, as night sleep should appear different than day sleep\n", "# after the baby begins to differentiate day from night (which can take longer\n", "# than parents might wish!)\n", "night_start_hour = 19\n", "night_end_hour = 7\n", "\n", "sleep_start, sleep_end, days_old"]}, {"cell_type": "markdown", "id": "82af8cbb-4c4d-4172-9d6c-4b70cd88639e", "metadata": {"execution": {"iopub.execute_input": "2024-05-14T16:01:30.523558Z", "iopub.status.busy": "2024-05-14T16:01:30.523558Z", "iopub.status.idle": "2024-05-14T16:01:30.599728Z", "shell.execute_reply": "2024-05-14T16:01:30.599728Z", "shell.execute_reply.started": "2024-05-14T16:01:30.523558Z"}}, "source": ["Setting up our random number generator:"]}, {"cell_type": "code", "execution_count": 3, "id": "5f4483f5-d558-4319-9c58-f6b6834f8bd2", "metadata": {"execution": {"iopub.execute_input": "2024-05-14T16:16:00.775295Z", "iopub.status.busy": "2024-05-14T16:16:00.775295Z", "iopub.status.idle": "2024-05-14T16:16:00.795211Z", "shell.execute_reply": "2024-05-14T16:16:00.795211Z", "shell.execute_reply.started": "2024-05-14T16:16:00.775295Z"}}, "outputs": [{"data": {"text/plain": ["Generator(PCG64) at 0x23805B69D20"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["rng = np.random.default_rng(738)\n", "# https://numpy.org/doc/stable/reference/random/index.html?highlight=random#module-numpy.random\n", "rng"]}, {"cell_type": "markdown", "id": "d89942b8-5897-4c6b-a299-ccc4c7361549", "metadata": {}, "source": ["Initializing df_sleep with the sleep data created above:"]}, {"cell_type": "code", "execution_count": 4, "id": "6f11f804-6abb-48d2-aabf-5ac7a1b75831", "metadata": {"execution": {"iopub.execute_input": "2024-05-14T16:16:00.797215Z", "iopub.status.busy": "2024-05-14T16:16:00.797215Z", "iopub.status.idle": "2024-05-14T16:16:00.806314Z", "shell.execute_reply": "2024-05-14T16:16:00.806314Z", "shell.execute_reply.started": "2024-05-14T16:16:00.797215Z"}}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Sleep Start</th>\n", "      <th>Sleep End</th>\n", "      <th>days_old</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2024-01-15 16:31:00</td>\n", "      <td>2024-01-15 17:43:00</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["          Sleep Start           Sleep End  days_old\n", "0 2024-01-15 16:31:00 2024-01-15 17:43:00         0"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["df_sleep = pd.DataFrame(\n", "    index = [0], data = {\n", "        'Sleep Start':sleep_start, 'Sleep End':sleep_end, \n", "        'days_old':days_old})\n", "df_sleep"]}, {"cell_type": "markdown", "id": "2f97bd73-4734-4747-8092-6caed11ad2f9", "metadata": {}, "source": ["## Creating a function that adds a new row of sleep data to the dataset:\n", "\n", "This function applies the following assumptions when calculating new sleep entries:\n", "\n", "1. The time in between sleep periods will increase with age.\n", "2. The length of the initial nighttime sleep period will also increase with age.\n", "3. Subsequent nighttime periods will be shorter than the initial periods.\n", "4. The onset of nighttime sleep should decrease with age.\n", "\n", "The function also adds random values to the sleep data so that it will appear more believable."]}, {"cell_type": "code", "execution_count": 5, "id": "e102c92f-24e4-4540-861b-3b42fc6f1a75", "metadata": {"execution": {"iopub.execute_input": "2024-05-14T16:16:00.807323Z", "iopub.status.busy": "2024-05-14T16:16:00.807323Z", "iopub.status.idle": "2024-05-14T16:16:00.814232Z", "shell.execute_reply": "2024-05-14T16:16:00.814232Z", "shell.execute_reply.started": "2024-05-14T16:16:00.807323Z"}}, "outputs": [], "source": ["def add_sleep_entry_to_dataset(df_sleep):\n", "    # Identifying the most recent sleep_start, sleep_end, and days_old\n", "    # values within the dataset:\n", "    latest_row = df_sleep.iloc[-1].copy()\n", "    previous_sleep_start = latest_row['Sleep Start']\n", "    if (previous_sleep_start.hour >= night_start_hour) | (\n", "        previous_sleep_start.hour <= night_end_hour):\n", "        previous_sleep_type = 'nighttime'    \n", "    else:\n", "        previous_sleep_type = 'daytime'\n", "    previous_sleep_end = start_time = latest_row['Sleep End']\n", "    previous_days_old = latest_row['days_old']\n", "    # Determining how long the baby will be awake between sleep periods:\n", "    # The following code calculates this period as a base time of \n", "    # around 48 to 72 minutes plus a random\n", "    # value that will increase as the child gets older.\n", "    # It also limits the maximum awake time at 16 hours, though I imagine\n", "    # that it will take most children quite a while to stay up this long.\n", "    awake_time = int(\n", "        min(60 * (0.8 + rng.random() * 0.4) \n", "            + (previous_days_old /2) * rng.random(), 960))\n", "    # Note: rng.random() returns a value greater than or equal to 0 \n", "    # and less than 1, encompasses the range [0,  1). Therefore, \n", "    # in the above code, 60 will get multiplied by a value greater than or \n", "    # equal to 0.8 and less than 1.2 (resulting in a base time \n", "    # of ~48-72 minutes).\n", "    sleep_start = previous_sleep_end + pd.to_timedelta(\n", "        awake_time, unit = 'minutes')\n", "    if (sleep_start.hour >= night_start_hour) | (\n", "        sleep_start.hour <= night_end_hour):\n", "        sleep_type = 'nighttime'\n", "    else:\n", "        sleep_type = 'daytime'\n", "    if sleep_type == 'nighttime':\n", "        \n", "        # The following line pushes the onset of sleep by a value equal to \n", "        # 84 minus 2 times the baby's age in days. This is meant to simulate\n", "        # an increasingly early bedtime as the child reaches 6 weeks of age.\n", "        # (<PERSON>, in Healthy Sleep Habits, <PERSON> Child, notes\n", "        # that bedtimes can get earlier after babies turn 6 weeks old,\n", "        # although this simulation creates a linear relationship between\n", "        # age and bedtime onset rather than a shift at 6 weeks).\n", "        \n", "        sleep_start += pd.to_timedelta(\n", "            max((168 - previous_days_old * 4), 0), unit = 'minutes')\n", "        if previous_sleep_type != 'nighttime': \n", "            # In this case, this period will be the baby's\n", "            # first sleep period to start at night, so we'll make it \n", "            # longer than subsequent nighttime sleep periods.\n", "            # We'll assume that, by 16 weeks (112 days), our fictional \n", "            # infant will be able to sleep\n", "            # up to 12 hours each night. (This won't be the case for many \n", "            # babies, of course!!)\n", "            # Thus, we'll start with a baseline sleep period of around\n", "            # 48-60 minutes, then extend this period by up to 11 hours \n", "            # depending on the child's age.\n", "            # Since 660 (11 hours in minutes) / 112 days equals \n", "            # around 5.9, we'll add\n", "            # 5.9 * the child's age (in days) * a random value between \n", "            # 0 and 1 to our sleep total.\n", "            # We'll also limit this value to 660 so that the baby \n", "            # won't continue to sleep longer\n", "            # and longer as he/she gets older.\n", "            sleep_duration = int(\n", "                (60 * (0.8 + rng.random() * 0.2)) \n", "                + min(previous_days_old * 5.9 * \n", "                      (0.3 + rng.random() * 0.7), 660))\n", "        else: # In this case, our baby already had at least one nighttime \n", "            # sleep period,so this one will be restricted to a duration \n", "            # of around 144 to 180 minutes.\n", "            sleep_duration = int(180 * (0.8 + rng.random() * 0.2))\n", "            \n", "        \n", "    else: # In this case, the child's sleep started during the daytime, \n", "        # so we'll simulate a nap \n", "        # by multiplying 120 minutes by a value in the range\n", "        # [0.8, 1.2).\n", "        sleep_duration = int((120 * (0.8 + rng.random() * 0.4)))\n", "        \n", "    sleep_end = sleep_start + pd.to_timedelta(sleep_duration, unit = 'minutes')       \n", "\n", "    # Creating the days_old value that corresponds to the time that sleep began:\n", "\n", "    days_old = (sleep_start - datob).days\n", "    \n", "    # print(sleep_start, sleep_end, sleep_duration, days_old)   \n", "    new_index = df_sleep.index[-1] + 1\n", "    df_sleep.loc[new_index] = (\n", "        {'Sleep Start':sleep_start, \n", "        'Sleep End':sleep_end, \n", "        'days_old':days_old})\n", "    "]}, {"cell_type": "markdown", "id": "473b3e09-3541-4448-98c4-9f764ef22d0b", "metadata": {}, "source": ["Extending our sleep table by running add_sleep_entry_to_dataset until our fictional baby is 180 days old:"]}, {"cell_type": "code", "execution_count": 6, "id": "80a14e89-8375-4bfd-828a-5e6dbfa67dd6", "metadata": {"execution": {"iopub.execute_input": "2024-05-14T16:16:00.815237Z", "iopub.status.busy": "2024-05-14T16:16:00.815237Z", "iopub.status.idle": "2024-05-14T16:16:01.771133Z", "shell.execute_reply": "2024-05-14T16:16:01.770331Z", "shell.execute_reply.started": "2024-05-14T16:16:00.815237Z"}}, "outputs": [], "source": ["while df_sleep.iloc[-1]['days_old'] <= 180:\n", "    # print(df_sleep.iloc[-1]['days_old'])\n", "    add_sleep_entry_to_dataset(df_sleep)"]}, {"cell_type": "markdown", "id": "720a087c-8c07-40c9-814a-5900e3a886f3", "metadata": {}, "source": ["Now that we've created the dataset, we can remove the days_old column, since our sleep analysis script will create a similar field."]}, {"cell_type": "code", "execution_count": 7, "id": "78dfd835-5c61-4184-bb14-d1c6265f6b5d", "metadata": {"execution": {"iopub.execute_input": "2024-05-14T16:16:01.772147Z", "iopub.status.busy": "2024-05-14T16:16:01.771133Z", "iopub.status.idle": "2024-05-14T16:16:01.779439Z", "shell.execute_reply": "2024-05-14T16:16:01.779439Z", "shell.execute_reply.started": "2024-05-14T16:16:01.772147Z"}}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Sleep Start</th>\n", "      <th>Sleep End</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2024-01-15 16:31:00</td>\n", "      <td>2024-01-15 17:43:00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2024-01-15 18:46:00</td>\n", "      <td>2024-01-15 21:01:00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2024-01-16 01:00:00</td>\n", "      <td>2024-01-16 01:49:00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2024-01-16 05:48:00</td>\n", "      <td>2024-01-16 08:44:00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2024-01-16 09:53:00</td>\n", "      <td>2024-01-16 12:06:00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>956</th>\n", "      <td>2024-07-13 19:09:00</td>\n", "      <td>2024-07-14 01:52:00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>957</th>\n", "      <td>2024-07-14 04:18:00</td>\n", "      <td>2024-07-14 06:42:00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>958</th>\n", "      <td>2024-07-14 08:38:00</td>\n", "      <td>2024-07-14 10:55:00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>959</th>\n", "      <td>2024-07-14 13:13:00</td>\n", "      <td>2024-07-14 15:27:00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>960</th>\n", "      <td>2024-07-14 17:52:00</td>\n", "      <td>2024-07-14 19:54:00</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>961 rows × 2 columns</p>\n", "</div>"], "text/plain": ["            Sleep Start           Sleep End\n", "0   2024-01-15 16:31:00 2024-01-15 17:43:00\n", "1   2024-01-15 18:46:00 2024-01-15 21:01:00\n", "2   2024-01-16 01:00:00 2024-01-16 01:49:00\n", "3   2024-01-16 05:48:00 2024-01-16 08:44:00\n", "4   2024-01-16 09:53:00 2024-01-16 12:06:00\n", "..                  ...                 ...\n", "956 2024-07-13 19:09:00 2024-07-14 01:52:00\n", "957 2024-07-14 04:18:00 2024-07-14 06:42:00\n", "958 2024-07-14 08:38:00 2024-07-14 10:55:00\n", "959 2024-07-14 13:13:00 2024-07-14 15:27:00\n", "960 2024-07-14 17:52:00 2024-07-14 19:54:00\n", "\n", "[961 rows x 2 columns]"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["df_sleep.drop('days_old', axis = 1, inplace = True)\n", "df_sleep"]}, {"cell_type": "markdown", "id": "31eb6233-8abb-47f8-b1ec-82b0b6e697f2", "metadata": {}, "source": ["## Saving this dataset to a .csv file so that it can be processed by our Baby Sleep Analysis script:"]}, {"cell_type": "code", "execution_count": 8, "id": "5295a5aa-5fd8-4e1d-85c6-95007f0a38ad", "metadata": {"execution": {"iopub.execute_input": "2024-05-14T16:16:01.780443Z", "iopub.status.busy": "2024-05-14T16:16:01.780443Z", "iopub.status.idle": "2024-05-14T16:16:01.789763Z", "shell.execute_reply": "2024-05-14T16:16:01.789763Z", "shell.execute_reply.started": "2024-05-14T16:16:01.780443Z"}}, "outputs": [], "source": ["df_sleep.to_csv('sleep_dataset.csv', index = False)"]}, {"cell_type": "code", "execution_count": 9, "id": "2be0e289-fccb-4282-b9d8-33c8ac4b07d8", "metadata": {"execution": {"iopub.execute_input": "2024-05-14T16:16:01.790772Z", "iopub.status.busy": "2024-05-14T16:16:01.790772Z", "iopub.status.idle": "2024-05-14T16:16:01.796995Z", "shell.execute_reply": "2024-05-14T16:16:01.796995Z", "shell.execute_reply.started": "2024-05-14T16:16:01.790772Z"}}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Sleep Start</th>\n", "      <th>Sleep End</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2024-01-15 16:31:00</td>\n", "      <td>2024-01-15 17:43:00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2024-01-15 18:46:00</td>\n", "      <td>2024-01-15 21:01:00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2024-01-16 01:00:00</td>\n", "      <td>2024-01-16 01:49:00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2024-01-16 05:48:00</td>\n", "      <td>2024-01-16 08:44:00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2024-01-16 09:53:00</td>\n", "      <td>2024-01-16 12:06:00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>956</th>\n", "      <td>2024-07-13 19:09:00</td>\n", "      <td>2024-07-14 01:52:00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>957</th>\n", "      <td>2024-07-14 04:18:00</td>\n", "      <td>2024-07-14 06:42:00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>958</th>\n", "      <td>2024-07-14 08:38:00</td>\n", "      <td>2024-07-14 10:55:00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>959</th>\n", "      <td>2024-07-14 13:13:00</td>\n", "      <td>2024-07-14 15:27:00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>960</th>\n", "      <td>2024-07-14 17:52:00</td>\n", "      <td>2024-07-14 19:54:00</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>961 rows × 2 columns</p>\n", "</div>"], "text/plain": ["            Sleep Start           Sleep End\n", "0   2024-01-15 16:31:00 2024-01-15 17:43:00\n", "1   2024-01-15 18:46:00 2024-01-15 21:01:00\n", "2   2024-01-16 01:00:00 2024-01-16 01:49:00\n", "3   2024-01-16 05:48:00 2024-01-16 08:44:00\n", "4   2024-01-16 09:53:00 2024-01-16 12:06:00\n", "..                  ...                 ...\n", "956 2024-07-13 19:09:00 2024-07-14 01:52:00\n", "957 2024-07-14 04:18:00 2024-07-14 06:42:00\n", "958 2024-07-14 08:38:00 2024-07-14 10:55:00\n", "959 2024-07-14 13:13:00 2024-07-14 15:27:00\n", "960 2024-07-14 17:52:00 2024-07-14 19:54:00\n", "\n", "[961 rows x 2 columns]"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["df_sleep"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.1"}}, "nbformat": 4, "nbformat_minor": 5}